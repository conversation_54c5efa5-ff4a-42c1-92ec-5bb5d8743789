
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import CalendarModal from './CalendarModal';

const ServicesSection = () => {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const services = [
    {
      icon: '⚡',
      title: 'MVP Development',
      description: 'Rapidly prototype and validate your ideas with a Minimum Viable Product (MVP). We help you launch quickly and iterate based on real user feedback.',
      features: ['Rapid prototyping', 'User validation', 'Iterative development', 'Market testing']
    },
    {
      icon: '🌐',
      title: 'Custom Web Solutions',
      description: 'Build bespoke websites tailored to your unique business needs. From design to development, we handle it all.',
      features: ['Custom design', 'Responsive layouts', 'Modern frameworks', 'Performance optimized']
    },
    {
      icon: '🎯',
      title: 'Landing Pages',
      description: 'Create high-converting landing pages that capture leads and drive conversions. Our designs are optimized for performance and engagement.',
      features: ['Conversion focused', 'A/B testing', 'Mobile optimized', 'Fast loading']
    },
    {
      icon: '☁️',
      title: 'SaaS Platforms',
      description: 'Develop scalable Software-as-a-Service (SaaS) solutions that meet your business requirements. We build robust, secure, and user-friendly platforms.',
      features: ['Scalable architecture', 'Security first', 'User management', 'API integration']
    },
    {
      icon: '📊',
      title: 'Digital Strategy Consulting',
      description: 'Need guidance on how to leverage technology for growth? Our experts provide strategic consulting to help you navigate the digital landscape.',
      features: ['Strategic planning', 'Technology roadmap', 'Growth analysis', 'Best practices']
    },
    {
      icon: '🔧',
      title: 'Technical Support',
      description: 'Ongoing technical support and maintenance to ensure your digital solutions continue to perform at their best.',
      features: ['24/7 monitoring', 'Regular updates', 'Bug fixes', 'Performance optimization']
    }
  ];

  return (
    <section id="services" className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-gray-100 to-gray-200 dark:from-gray-900 dark:to-black">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl text-gray-900 dark:text-white mb-4 sm:mb-6">
            Our <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Services</span>
          </h2>
          <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4 sm:px-0">
            We offer a comprehensive range of digital services to help your business thrive in the modern marketplace.
          </p>
        </div>

        <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {services.map((service, index) => (
            <Card
              key={index}
              className="bg-white/80 dark:bg-white/5 border border-gray-200 dark:border-white/10 backdrop-blur-lg hover:bg-white dark:hover:bg-white/10 transition-all duration-300 hover:transform hover:scale-105 group"
            >
              <CardHeader className="text-center px-4 sm:px-6">
                <div className="text-4xl sm:text-5xl md:text-6xl mb-4 group-hover:animate-float">{service.icon}</div>
                <CardTitle className="font-montserrat text-xl sm:text-2xl text-gray-900 dark:text-white group-hover:text-primary transition-colors">
                  {service.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 px-4 sm:px-6">
                <p className="font-poppins text-gray-600 dark:text-gray-300 text-center text-sm sm:text-base">
                  {service.description}
                </p>
                <ul className="space-y-2">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <span className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0"></span>
                      {feature}
                    </li>
                  ))}
                </ul>

              </CardContent>
            </Card>
          ))}
        </div>

        {/* Single CTA Section */}
        <div className="text-center mt-12 sm:mt-16">
          <div className="max-w-2xl mx-auto mb-8">
            <h3 className="font-montserrat font-bold text-2xl sm:text-3xl text-gray-900 dark:text-white mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="font-poppins text-gray-600 dark:text-gray-300 text-base sm:text-lg">
              Let's discuss how our services can help you achieve your goals. Book a free consultation today.
            </p>
          </div>
          <Button
            onClick={() => setIsCalendarOpen(true)}
            size="lg"
            className="bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-white font-semibold px-8 py-3 text-lg transition-all duration-300 transform hover:scale-105"
          >
            Book a Free Consultation
          </Button>
        </div>
      </div>

      <CalendarModal
        isOpen={isCalendarOpen}
        onClose={() => setIsCalendarOpen(false)}
      />
    </section>
  );
};

export default ServicesSection;
