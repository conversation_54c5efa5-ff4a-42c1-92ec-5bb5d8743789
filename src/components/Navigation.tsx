
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Menu, X } from 'lucide-react';
import { ThemeToggle } from '@/components/ThemeToggle';
import CalendarModal from '@/components/CalendarModal';

const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMobileMenuOpen(false); // Close mobile menu when navigating
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled ? 'bg-white/20 dark:bg-black/20 backdrop-blur-xl border-b border-white/30 dark:border-white/20 shadow-lg' : 'bg-white/10 dark:bg-black/10 backdrop-blur-md'
    }`}>
      <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
        <div className="flex items-center justify-between">
          <div className="font-montserrat font-bold text-xl sm:text-2xl text-gray-900 dark:text-white">
            <span className="text-primary">M</span>BI
          </div>

          {/* Desktop Navigation - Centered */}
          <div className="hidden md:flex items-center justify-center space-x-8 absolute left-1/2 transform -translate-x-1/2">
            <button
              onClick={() => scrollToSection('home')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              Home
            </button>
            <button
              onClick={() => scrollToSection('about')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              About
            </button>
            <button
              onClick={() => scrollToSection('services')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              Services
            </button>
            <button
              onClick={() => scrollToSection('portfolio')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              Portfolio
            </button>
            <button
              onClick={() => scrollToSection('pricing')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              Pricing
            </button>
            <button
              onClick={() => scrollToSection('contact')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              Contact
            </button>
          </div>

          {/* Desktop Actions */}
          <div className="hidden sm:flex items-center gap-4">
            <ThemeToggle />
            <Button
              onClick={() => setIsCalendarOpen(true)}
              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-poppins px-4 sm:px-6 py-2 rounded-full transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25 text-sm sm:text-base backdrop-blur-md border border-white/20"
            >
              Book a Call
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden text-gray-900 dark:text-white p-2"
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden absolute top-full left-0 right-0 bg-white/20 dark:bg-black/20 backdrop-blur-xl border-b border-white/30 dark:border-white/20 shadow-lg">
            <div className="flex flex-col space-y-4 px-4 py-6">
              <button
                onClick={() => scrollToSection('home')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection('about')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                About
              </button>
              <button
                onClick={() => scrollToSection('services')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                Services
              </button>
              <button
                onClick={() => scrollToSection('portfolio')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                Portfolio
              </button>
              <button
                onClick={() => scrollToSection('pricing')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                Pricing
              </button>
              <button
                onClick={() => scrollToSection('contact')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                Contact
              </button>
              <div className="flex items-center justify-between mt-4">
                <ThemeToggle />
                <Button
                  onClick={() => {
                    setIsCalendarOpen(true);
                    setIsMobileMenuOpen(false);
                  }}
                  className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-poppins px-6 py-3 rounded-full transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25 backdrop-blur-md border border-white/20"
                >
                  Book a Call
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Calendar Modal */}
      <CalendarModal
        isOpen={isCalendarOpen}
        onClose={() => setIsCalendarOpen(false)}
      />
    </nav>
  );
};

export default Navigation;
