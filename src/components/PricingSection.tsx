
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import CalendarModal from './CalendarModal';

const PricingSection = () => {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const plans = [
    {
      name: 'Basic Package',
      price: '$2,999',
      period: 'per project',
      description: 'Perfect for startups and small businesses looking to get started.',
      features: [
        'MVP Development',
        'Landing Page Design',
        'Initial Consultation',
        'Basic Support (30 days)',
        'Responsive Design',
        'SEO Optimization'
      ],
      highlighted: false,
      cta: 'Get Started'
    },
    {
      name: 'Pro Package',
      price: '$5,999',
      period: 'per project',
      description: 'Ideal for growing businesses that need comprehensive solutions.',
      features: [
        'Everything in Basic',
        'Custom Web Solutions',
        'SaaS Platform Development',
        'Extended Support (90 days)',
        'Advanced Analytics',
        'API Integration',
        'Performance Optimization',
        'Security Implementation'
      ],
      highlighted: true,
      cta: 'Most Popular'
    },
    {
      name: 'Enterprise Package',
      price: 'Custom',
      period: 'tailored pricing',
      description: 'Comprehensive solutions for large businesses and enterprises.',
      features: [
        'Everything in Pro',
        'Digital Strategy Consulting',
        'Long-term Partnership',
        'Dedicated Project Manager',
        '24/7 Priority Support',
        'Advanced Security Features',
        'Scalability Planning',
        'Custom Integrations'
      ],
      highlighted: false,
      cta: 'Contact Us'
    }
  ];

  return (
    <section id="pricing" className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-gray-100 to-gray-200 dark:from-gray-900 dark:to-black">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl text-gray-900 dark:text-white mb-4 sm:mb-6">
            Transparent <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Pricing</span>
          </h2>
          <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4 sm:px-0">
            Choose the perfect package for your business needs. All packages include our commitment to quality and innovation.
          </p>
        </div>
        
        <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 max-w-6xl mx-auto px-4 sm:px-0">
          {plans.map((plan, index) => (
            <Card
              key={index}
              className={`relative overflow-hidden transition-all duration-300 hover:transform hover:scale-105 ${
                plan.highlighted
                  ? 'bg-gradient-to-b from-white to-gray-50 dark:from-gray-800/90 dark:to-gray-900/90 border-2 border-primary scale-105 shadow-xl shadow-primary/20'
                  : 'bg-white/80 dark:bg-white/5 border border-gray-200 dark:border-white/10'
              } backdrop-blur-lg`}
            >
              {plan.highlighted && (
                <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-primary to-accent text-white text-center py-2 text-sm font-poppins font-semibold">
                  MOST POPULAR
                </div>
              )}

              <CardHeader className={`text-center ${plan.highlighted ? 'pt-12' : 'pt-8'} px-4 sm:px-6`}>
                <CardTitle className="font-montserrat text-xl sm:text-2xl text-gray-900 dark:text-white mb-2">
                  {plan.name}
                </CardTitle>
                <div className="mb-4">
                  <span className="font-montserrat font-bold text-3xl sm:text-4xl text-gray-900 dark:text-white">
                    {plan.price}
                  </span>
                  <span className={`font-poppins ml-2 text-sm ${plan.highlighted ? 'text-gray-600 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'}`}>
                    {plan.period}
                  </span>
                </div>
                <p className={`font-poppins text-sm ${plan.highlighted ? 'text-gray-700 dark:text-gray-200' : 'text-gray-600 dark:text-gray-300'}`}>
                  {plan.description}
                </p>
              </CardHeader>
              
              <CardContent className="space-y-6 px-4 sm:px-6">
                <ul className="space-y-3">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className={`flex items-center font-poppins text-sm ${plan.highlighted ? 'text-gray-700 dark:text-gray-200' : 'text-gray-600 dark:text-gray-300'}`}>
                      <span className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0"></span>
                      {feature}
                    </li>
                  ))}
                </ul>

                <Button
                  className={`w-full py-3 font-poppins font-semibold transition-all duration-300 ${
                    plan.highlighted
                      ? 'bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white shadow-lg'
                      : 'bg-gray-100 dark:bg-white/10 hover:bg-gray-200 dark:hover:bg-white/20 text-gray-900 dark:text-white border border-gray-300 dark:border-white/20'
                  }`}
                >
                  {plan.cta}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <p className="font-poppins text-gray-400 mb-4">
            Need something custom? We're here to help.
          </p>
          <Button
            variant="ghost"
            className="text-primary border border-primary/30 hover:bg-primary/10 hover:border-primary/50 font-poppins px-6 py-2"
            onClick={() => setIsCalendarOpen(true)}
          >
            Schedule a Consultation
          </Button>
        </div>
      </div>

      <CalendarModal
        isOpen={isCalendarOpen}
        onClose={() => setIsCalendarOpen(false)}
      />
    </section>
  );
};

export default PricingSection;
