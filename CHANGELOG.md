# Changelog

All notable changes to the Millennial Business Innovations website will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.3.0] - 2024-12-19

### Changed
- **Default Theme to Light Mode**
  - Changed default theme from "system" to "light" for consistent user experience
  - Users can still toggle between light/dark/system themes using the theme toggle
  - Provides a clean, professional first impression with light theme

### Fixed
- **Calendar Integration in Pricing Section**
  - Fixed "Schedule a Consultation" button to properly open calendar modal
  - Added consistent calendar popup behavior across all booking buttons
  - Integrated GHL calendar widget with proper state management

### Improved
- **Professional Tech Stack Icons**
  - Replaced emoji icons with high-quality SVG icons from react-icons library
  - Added official brand colors for each technology (React blue, TypeScript blue, etc.)
  - Enhanced visual consistency and professional appearance
  - Icons include: React, Next.js, TypeScript, Node.js, Python, MongoDB, PostgreSQL, AWS, Docker, Figma

### Technical Improvements
- **Dependencies**: Added react-icons package for professional icon library
- **Component Architecture**: Streamlined icon implementation with industry-standard icons
- **Theme Management**: Fixed Sonner component to use custom ThemeProvider
- **Code Cleanup**: Removed custom icon components in favor of react-icons

## [1.2.0] - 2024-12-19

### Fixed
- **Calendar Modal Sizing Issues**
  - Fixed calendar not being fully viewable by increasing modal size (max-w-5xl, h-90vh)
  - Removed duplicate X buttons by eliminating custom header overlay
  - Enabled scrolling for better calendar navigation on mobile devices
  - Improved modal responsiveness with w-95vw for mobile screens
  - Enhanced visual hierarchy with transparent background and shadow effects

### Added
- **Dedicated Portfolio Page**
  - Created comprehensive `/portfolio` page showcasing all 9 projects
  - Added new projects: Denti-Nexus Clinic Suite, Stephen Lovino Portfolio, MBI Corporate Site
  - Professional portfolio page with back navigation and theme toggle
  - Enhanced project cards with external link buttons and improved layouts
  - Responsive grid system optimized for all screen sizes

### Changed
- **Portfolio Section Optimization**
  - Reduced main portfolio section to 3 featured projects for better focus
  - Featured projects: Aha-Innovations Platform, RR Twins, Denti-Nexus Clinic Suite
  - "View All Projects" button now navigates to dedicated portfolio page
  - Implemented React Router navigation for seamless page transitions

### Technical Improvements
- **Routing Enhancement**: Added `/portfolio` route to App.tsx configuration
- **Component Architecture**: Created modular Portfolio page component
- **Navigation**: Professional header with back navigation on portfolio page
- **Performance**: Optimized project loading and image handling

### New Projects Showcase
- **Denti-Nexus Clinic Suite**: Healthcare platform with appointment scheduling
- **Stephen Lovino Portfolio**: Professional developer portfolio website
- **MBI Corporate Site**: Corporate website for Millennial Business Innovations
- **Enhanced Project Details**: Better descriptions and technology stack displays

## [1.1.1] - 2024-12-19

### Fixed
- **Mobile Header Overlap Issue**
  - Fixed navigation header overlapping with announcement banner on real mobile devices
  - Added proper top padding (pt-20 sm:pt-24) to hero section to account for fixed navigation
  - Enhanced navigation background with semi-transparent backdrop for better visibility
  - Improved mobile navigation spacing and backdrop blur effects

### Added
- **GHL Calendar Integration**
  - Created CalendarModal component for seamless calendar booking
  - Integrated GoHighLevel calendar widget (https://calendar.aha-innovations.com/widget/booking/muwcL091TjKbXukSZgs5)
  - Added calendar modal to both Navigation and HeroSection components
  - Professional calendar popup with proper styling and responsive design

### Changed
- **Enhanced User Experience**
  - Updated all "Book a Call" buttons to open calendar modal instead of scrolling to contact section
  - Improved navigation transparency and backdrop effects
  - Better mobile menu interaction with calendar integration
  - Calendar modal automatically loads GHL embed script when opened

### Technical Details
- **New Component**: `src/components/CalendarModal.tsx`
- **Calendar Integration**: Uses iframe embed with dynamic script loading
- **Modal System**: Built with shadcn/ui Dialog component
- **Responsive Design**: Calendar modal adapts to different screen sizes
- **Clean Script Management**: Automatic script cleanup when modal closes

## [1.1.0] - 2024-12-19

### Added
- **Complete Theme System Implementation**
  - Light/Dark/System theme toggle with persistent storage
  - Custom ThemeProvider component with React Context
  - ThemeToggle dropdown component with sun/moon icons
  - Theme toggle available in both desktop and mobile navigation
  - Automatic system theme detection and preference following

- **Comprehensive Theme-Aware Styling**
  - All sections now properly support both light and dark modes
  - Smooth transitions between theme changes
  - Proper contrast ratios for accessibility compliance
  - CSS variables integration with Tailwind's dark mode system

### Changed
- **Hero Section Content Updates**
  - Main heading: "We help founders turn ideas into seamless digital experiences" → "You dream it. We build it."
  - Subtext: "Hello, We're Millennial Business Innovations 🚀 a Full Stack Developer" → "Helping founders launch MVPs and digital products without the tech overwhelm."
  - Greeting: Added "👋 Hello, we're Millennial Business Innovations your full-stack product team." (removed em dash)
  - Applied gradient typography to "digital products" in subtext

- **Theme-Aware Component Updates**
  - **HeroSection**: Light gray gradients, adaptive text colors, theme-aware buttons
  - **AboutSection**: Light backgrounds, semi-transparent cards, adaptive text
  - **ServicesSection**: Light mode backgrounds, card styling updates
  - **PortfolioSection**: Adaptive backgrounds, border color updates
  - **PricingSection**: Light mode card styling, button color adaptations
  - **ContactSection**: Form input styling, card background updates
  - **Footer**: Light mode backgrounds, adaptive text and link colors
  - **Navigation**: Adaptive navigation bar, mobile menu theme support

- **CSS Variables Enhancement**
  - Updated light mode background from pure white to light gray (98%)
  - Enhanced dark mode with true black backgrounds
  - Improved color contrast for better readability

### Technical Details
- **Theme Storage**: Uses localStorage with key "mbi-ui-theme"
- **Default Theme**: System preference detection
- **CSS Framework**: Tailwind CSS with class-based dark mode
- **State Management**: React Context API for theme state
- **Hot Module Reload**: All changes support HMR for development

### Files Modified
- `src/components/ThemeProvider.tsx` (new)
- `src/components/ThemeToggle.tsx` (new)
- `src/App.tsx`
- `src/components/Navigation.tsx`
- `src/components/HeroSection.tsx`
- `src/components/AboutSection.tsx`
- `src/components/ServicesSection.tsx`
- `src/components/PortfolioSection.tsx`
- `src/components/PricingSection.tsx`
- `src/components/ContactSection.tsx`
- `src/components/Footer.tsx`
- `src/pages/Index.tsx`
- `src/index.css`

### Dependencies
- Existing `next-themes` package utilized for theme management
- No new dependencies added

## [1.0.0] - 2024-12-19

### Added
- Initial website launch
- Hero section with company branding
- About section with mission and vision
- Services showcase
- Portfolio gallery
- Pricing plans
- Contact form
- Footer with company information
- Responsive design for all screen sizes
- Modern UI with Tailwind CSS and shadcn/ui components

### Features
- Single-page application with smooth scrolling navigation
- Contact form with validation
- Responsive grid layouts
- Modern typography with Montserrat and Poppins fonts
- Gradient backgrounds and animations
- Mobile-first responsive design
