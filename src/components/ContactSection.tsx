
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import ContactIcon from './ContactIcon';

const ContactSection = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name || !formData.email || !formData.message) {
      toast({
        title: "Please fill in all fields",
        description: "All fields are required to submit the form.",
        variant: "destructive"
      });
      return;
    }

    // Simulate form submission
    toast({
      title: "Message sent successfully!",
      description: "We'll get back to you within 24 hours.",
    });
    
    // Reset form
    setFormData({ name: '', email: '', message: '' });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <section id="contact" className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-teal-50 via-cyan-50 to-blue-50 dark:from-black dark:via-teal-900/10 dark:to-cyan-900/10 relative overflow-hidden">
      {/* Glassmorphism background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-10 w-44 h-44 bg-gradient-to-r from-teal-300/20 to-cyan-300/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 left-10 w-56 h-56 bg-gradient-to-r from-cyan-300/15 to-blue-300/15 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 right-1/3 w-28 h-28 bg-gradient-to-r from-blue-300/10 to-teal-300/10 rounded-full blur-lg"></div>
      </div>
      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl text-gray-900 dark:text-white mb-4 sm:mb-6">
            Let's <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Connect</span>
          </h2>
          <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4 sm:px-0">
            Ready to transform your ideas into digital reality? Get in touch with us today and let's discuss your project.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 max-w-6xl mx-auto">
          {/* Contact Information */}
          <div className="space-y-6 sm:space-y-8">
            <Card className="bg-white/5 border border-white/10 backdrop-blur-lg">
              <CardContent className="p-4 sm:p-6 md:p-8">
                <h3 className="font-montserrat font-semibold text-xl sm:text-2xl text-white mb-4 sm:mb-6">
                  Get in Touch
                </h3>

                <div className="space-y-4 sm:space-y-6">
                  <div className="flex items-center space-x-3 sm:space-x-4">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/20 rounded-full flex items-center justify-center text-sm sm:text-base">
                      <ContactIcon type="email" className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-poppins font-semibold text-gray-900 dark:text-white text-sm sm:text-base">Email</h4>
                      <p className="font-poppins text-gray-600 dark:text-gray-300 text-xs sm:text-sm break-all"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 sm:space-x-4">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/20 rounded-full flex items-center justify-center text-sm sm:text-base">
                      <ContactIcon type="phone" className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-poppins font-semibold text-gray-900 dark:text-white text-sm sm:text-base">Phone</h4>
                      <p className="font-poppins text-gray-600 dark:text-gray-300 text-xs sm:text-sm">+****************</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 sm:space-x-4">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/20 rounded-full flex items-center justify-center text-sm sm:text-base">
                      <ContactIcon type="social" className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-poppins font-semibold text-gray-900 dark:text-white text-sm sm:text-base">Social Media</h4>
                      <div className="flex flex-wrap gap-2 sm:gap-3 mt-2">
                        <Button variant="ghost" size="sm" className="text-gray-600 dark:text-gray-300 hover:text-primary text-xs sm:text-sm px-2 sm:px-3">
                          LinkedIn
                        </Button>
                        <Button variant="ghost" size="sm" className="text-gray-600 dark:text-gray-300 hover:text-primary text-xs sm:text-sm px-2 sm:px-3">
                          Twitter
                        </Button>
                        <Button variant="ghost" size="sm" className="text-gray-600 dark:text-gray-300 hover:text-primary text-xs sm:text-sm px-2 sm:px-3">
                          Instagram
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-white/80 dark:bg-white/5 border border-gray-200 dark:border-white/10 backdrop-blur-lg">
              <CardContent className="p-4 sm:p-6 md:p-8">
                <h3 className="font-montserrat font-semibold text-xl sm:text-2xl text-gray-900 dark:text-white mb-3 sm:mb-4">
                  Why Choose Us?
                </h3>
                <ul className="space-y-2 sm:space-y-3">
                  {[
                    'Experienced team of developers and designers',
                    'Proven track record with 50+ successful projects',
                    'Fast turnaround times without compromising quality',
                    'Ongoing support and maintenance',
                    'Transparent pricing and communication'
                  ].map((item, index) => (
                    <li key={index} className="flex items-center text-gray-600 dark:text-gray-300 font-poppins text-sm sm:text-base">
                      <span className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0"></span>
                      {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <Card className="bg-white/80 dark:bg-white/5 border border-gray-200 dark:border-white/10 backdrop-blur-lg">
            <CardHeader className="p-4 sm:p-6">
              <CardTitle className="font-montserrat text-xl sm:text-2xl text-gray-900 dark:text-white">
                Send us a Message
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 sm:p-6">
              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                <div>
                  <Input
                    name="name"
                    placeholder="Your Name"
                    value={formData.name}
                    onChange={handleChange}
                    className="bg-gray-50 dark:bg-white/10 border-gray-300 dark:border-white/20 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-primary h-10 sm:h-12"
                  />
                </div>

                <div>
                  <Input
                    name="email"
                    type="email"
                    placeholder="Your Email"
                    value={formData.email}
                    onChange={handleChange}
                    className="bg-gray-50 dark:bg-white/10 border-gray-300 dark:border-white/20 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-primary h-10 sm:h-12"
                  />
                </div>

                <div>
                  <Textarea
                    name="message"
                    placeholder="Tell us about your project..."
                    rows={5}
                    value={formData.message}
                    onChange={handleChange}
                    className="bg-gray-50 dark:bg-white/10 border-gray-300 dark:border-white/20 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-primary resize-none min-h-[120px] sm:min-h-[140px]"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins py-2 sm:py-3 text-sm sm:text-base"
                >
                  Send Message
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
